import 'dart:io';
import 'dart:typed_data';
import 'package:flutter/foundation.dart';
import 'package:path_provider/path_provider.dart';
import 'package:share_plus/share_plus.dart';
import 'package:permission_handler/permission_handler.dart';
import 'package:file_picker/file_picker.dart';
import '../models/qr_code_data.dart';
import 'qr_generator_service.dart';

/// Service for handling file operations, downloads, and sharing
class FileService {
  static const String _qrCodesFolder = 'QR_Codes';

  /// Save QR code to device storage
  static Future<String?> saveQRCodeToDevice({
    required QRCodeData qrData,
    required ExportFormat format,
    int size = 512,
  }) async {
    try {
      // Request storage permission on mobile platforms
      if (!kIsWeb && (Platform.isAndroid || Platform.isIOS)) {
        final permission = await _requestStoragePermission();
        if (!permission) {
          throw Exception('Storage permission denied');
        }
      }

      // Generate QR code bytes
      final qrBytes = await QRGeneratorService.generateQRCodeBytes(
        data: qrData.content,
        style: qrData.style,
        size: size,
        format: format,
      );

      // Get save directory
      final directory = await _getSaveDirectory();
      final filename = QRGeneratorService.getSuggestedFilename(qrData, format);
      final filePath = '${directory.path}/$filename';

      // Save file
      final file = File(filePath);
      await file.writeAsBytes(qrBytes);

      return filePath;
    } catch (e) {
      throw Exception('Failed to save QR code: $e');
    }
  }

  /// Share QR code
  static Future<void> shareQRCode({
    required QRCodeData qrData,
    required ExportFormat format,
    int size = 512,
  }) async {
    try {
      // Generate QR code bytes
      final qrBytes = await QRGeneratorService.generateQRCodeBytes(
        data: qrData.content,
        style: qrData.style,
        size: size,
        format: format,
      );

      // Create temporary file
      final tempDir = await getTemporaryDirectory();
      final filename = QRGeneratorService.getSuggestedFilename(qrData, format);
      final tempFile = File('${tempDir.path}/$filename');
      await tempFile.writeAsBytes(qrBytes);

      // Share file
      await Share.shareXFiles(
        [XFile(tempFile.path)],
        text: 'QR Code: ${qrData.title}',
        subject: qrData.title,
      );
    } catch (e) {
      throw Exception('Failed to share QR code: $e');
    }
  }

  /// Download QR code (for web platform)
  static Future<void> downloadQRCodeWeb({
    required QRCodeData qrData,
    required ExportFormat format,
    int size = 512,
  }) async {
    if (!kIsWeb) {
      throw Exception('This method is only for web platform');
    }

    try {
      // Generate QR code bytes
      final qrBytes = await QRGeneratorService.generateQRCodeBytes(
        data: qrData.content,
        style: qrData.style,
        size: size,
        format: format,
      );

      final filename = QRGeneratorService.getSuggestedFilename(qrData, format);
      
      // For web, we'll use the browser's download functionality
      // This would typically involve using dart:html, but for now we'll use a placeholder
      // In a real implementation, you'd use something like:
      // final blob = html.Blob([qrBytes]);
      // final url = html.Url.createObjectUrlFromBlob(blob);
      // final anchor = html.AnchorElement(href: url)..download = filename;
      // anchor.click();
      
      throw UnimplementedError('Web download not implemented in this demo');
    } catch (e) {
      throw Exception('Failed to download QR code: $e');
    }
  }

  /// Pick logo image from device
  static Future<String?> pickLogoImage() async {
    try {
      final result = await FilePicker.platform.pickFiles(
        type: FileType.image,
        allowMultiple: false,
      );

      if (result != null && result.files.isNotEmpty) {
        final file = result.files.first;
        if (file.path != null) {
          return file.path;
        } else if (file.bytes != null) {
          // For web platform, save bytes to temporary file
          final tempDir = await getTemporaryDirectory();
          final tempFile = File('${tempDir.path}/${file.name}');
          await tempFile.writeAsBytes(file.bytes!);
          return tempFile.path;
        }
      }
      return null;
    } catch (e) {
      throw Exception('Failed to pick logo image: $e');
    }
  }

  /// Get list of saved QR codes
  static Future<List<String>> getSavedQRCodes() async {
    try {
      final directory = await _getSaveDirectory();
      if (!await directory.exists()) {
        return [];
      }

      final files = directory.listSync()
          .where((entity) => entity is File)
          .map((entity) => entity.path)
          .where((path) => 
              path.endsWith('.png') || 
              path.endsWith('.svg') || 
              path.endsWith('.pdf'))
          .toList();

      return files;
    } catch (e) {
      return [];
    }
  }

  /// Delete saved QR code file
  static Future<bool> deleteSavedQRCode(String filePath) async {
    try {
      final file = File(filePath);
      if (await file.exists()) {
        await file.delete();
        return true;
      }
      return false;
    } catch (e) {
      return false;
    }
  }

  /// Get file size in human readable format
  static String getFileSize(String filePath) {
    try {
      final file = File(filePath);
      final bytes = file.lengthSync();
      
      if (bytes < 1024) {
        return '$bytes B';
      } else if (bytes < 1024 * 1024) {
        return '${(bytes / 1024).toStringAsFixed(1)} KB';
      } else {
        return '${(bytes / (1024 * 1024)).toStringAsFixed(1)} MB';
      }
    } catch (e) {
      return 'Unknown';
    }
  }

  /// Request storage permission
  static Future<bool> _requestStoragePermission() async {
    if (Platform.isAndroid) {
      final status = await Permission.storage.request();
      return status.isGranted;
    } else if (Platform.isIOS) {
      final status = await Permission.photos.request();
      return status.isGranted;
    }
    return true;
  }

  /// Get save directory
  static Future<Directory> _getSaveDirectory() async {
    if (kIsWeb) {
      return await getTemporaryDirectory();
    }

    if (Platform.isAndroid) {
      final directory = await getExternalStorageDirectory();
      if (directory != null) {
        final qrDirectory = Directory('${directory.path}/$_qrCodesFolder');
        if (!await qrDirectory.exists()) {
          await qrDirectory.create(recursive: true);
        }
        return qrDirectory;
      }
    }

    // Fallback to documents directory
    final directory = await getApplicationDocumentsDirectory();
    final qrDirectory = Directory('${directory.path}/$_qrCodesFolder');
    if (!await qrDirectory.exists()) {
      await qrDirectory.create(recursive: true);
    }
    return qrDirectory;
  }

  /// Export multiple QR codes as batch
  static Future<List<String>> exportBatchQRCodes({
    required List<QRCodeData> qrCodes,
    required ExportFormat format,
    int size = 512,
  }) async {
    final savedFiles = <String>[];
    
    for (final qrData in qrCodes) {
      try {
        final filePath = await saveQRCodeToDevice(
          qrData: qrData,
          format: format,
          size: size,
        );
        if (filePath != null) {
          savedFiles.add(filePath);
        }
      } catch (e) {
        // Continue with other files even if one fails
        continue;
      }
    }
    
    return savedFiles;
  }

  /// Get available storage space
  static Future<String> getAvailableStorage() async {
    try {
      if (kIsWeb) {
        return 'Web Storage';
      }

      final directory = await _getSaveDirectory();
      final stat = await directory.stat();
      
      // This is a simplified implementation
      // In a real app, you'd use platform-specific methods to get actual storage info
      return 'Available';
    } catch (e) {
      return 'Unknown';
    }
  }
}

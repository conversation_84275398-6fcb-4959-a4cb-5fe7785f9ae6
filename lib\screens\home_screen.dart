import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../providers/qr_provider.dart';
import '../widgets/qr_generator_widget.dart';
import '../widgets/qr_gallery_widget.dart';
import '../widgets/qr_customization_widget.dart';
import '../models/qr_code_data.dart';

/// Main home screen with navigation and QR code functionality
class HomeScreen extends StatefulWidget {
  const HomeScreen({super.key});

  @override
  State<HomeScreen> createState() => _HomeScreenState();
}

class _HomeScreenState extends State<HomeScreen> with TickerProviderStateMixin {
  late TabController _tabController;
  int _currentIndex = 0;

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 3, vsync: this);
    _tabController.addListener(() {
      setState(() {
        _currentIndex = _tabController.index;
      });
    });
  }

  @override
  void dispose() {
    _tabController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: NestedScrollView(
        headerSliverBuilder: (context, innerBoxIsScrolled) {
          return [
            SliverAppBar(
              expandedHeight: 120,
              floating: false,
              pinned: true,
              elevation: 0,
              backgroundColor: Theme.of(context).colorScheme.surface,
              flexibleSpace: FlexibleSpaceBar(
                title: Text(
                  'QR Code Generator',
                  style: Theme.of(context).textTheme.headlineSmall?.copyWith(
                    fontWeight: FontWeight.bold,
                    color: Theme.of(context).colorScheme.primary,
                  ),
                ),
                centerTitle: true,
                background: Container(
                  decoration: BoxDecoration(
                    gradient: LinearGradient(
                      begin: Alignment.topLeft,
                      end: Alignment.bottomRight,
                      colors: [
                        Theme.of(context).colorScheme.primary.withOpacity(0.1),
                        Theme.of(context).colorScheme.secondary.withOpacity(0.1),
                      ],
                    ),
                  ),
                ),
              ),
              bottom: TabBar(
                controller: _tabController,
                indicatorColor: Theme.of(context).colorScheme.primary,
                labelColor: Theme.of(context).colorScheme.primary,
                unselectedLabelColor: Colors.grey,
                tabs: const [
                  Tab(
                    icon: Icon(Icons.qr_code),
                    text: 'Generate',
                  ),
                  Tab(
                    icon: Icon(Icons.palette),
                    text: 'Customize',
                  ),
                  Tab(
                    icon: Icon(Icons.photo_library),
                    text: 'Gallery',
                  ),
                ],
              ),
            ),
          ];
        },
        body: Consumer<QRProvider>(
          builder: (context, qrProvider, child) {
            return TabBarView(
              controller: _tabController,
              children: [
                // Generate Tab
                const QRGeneratorWidget(),
                
                // Customize Tab
                const QRCustomizationWidget(),
                
                // Gallery Tab
                const QRGalleryWidget(),
              ],
            );
          },
        ),
      ),
      floatingActionButton: _currentIndex == 0
          ? Consumer<QRProvider>(
              builder: (context, qrProvider, child) {
                return FloatingActionButton.extended(
                  onPressed: qrProvider.isLoading
                      ? null
                      : () async {
                          await qrProvider.createQRCode();
                          if (qrProvider.error == null) {
                            _showSuccessSnackBar(context);
                          } else {
                            _showErrorSnackBar(context, qrProvider.error!);
                          }
                        },
                  icon: qrProvider.isLoading
                      ? const SizedBox(
                          width: 20,
                          height: 20,
                          child: CircularProgressIndicator(strokeWidth: 2),
                        )
                      : const Icon(Icons.add),
                  label: Text(qrProvider.isLoading ? 'Creating...' : 'Create QR'),
                  backgroundColor: Theme.of(context).colorScheme.primary,
                  foregroundColor: Colors.white,
                );
              },
            )
          : null,
      bottomNavigationBar: _buildBottomInfo(),
    );
  }

  Widget _buildBottomInfo() {
    return Consumer<QRProvider>(
      builder: (context, qrProvider, child) {
        if (qrProvider.error != null) {
          return Container(
            padding: const EdgeInsets.all(16),
            color: Theme.of(context).colorScheme.errorContainer,
            child: Row(
              children: [
                Icon(
                  Icons.error_outline,
                  color: Theme.of(context).colorScheme.error,
                ),
                const SizedBox(width: 8),
                Expanded(
                  child: Text(
                    qrProvider.error!,
                    style: TextStyle(
                      color: Theme.of(context).colorScheme.error,
                      fontWeight: FontWeight.w500,
                    ),
                  ),
                ),
                IconButton(
                  onPressed: qrProvider.clearError,
                  icon: const Icon(Icons.close),
                  color: Theme.of(context).colorScheme.error,
                ),
              ],
            ),
          );
        }

        return Container(
          padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
          decoration: BoxDecoration(
            color: Theme.of(context).colorScheme.surface,
            border: Border(
              top: BorderSide(
                color: Theme.of(context).dividerColor,
                width: 0.5,
              ),
            ),
          ),
          child: Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Text(
                'Total QR Codes: ${qrProvider.qrCodes.length}',
                style: Theme.of(context).textTheme.bodySmall?.copyWith(
                  color: Colors.grey[600],
                ),
              ),
              if (qrProvider.currentQRCode != null)
                Text(
                  'Type: ${_getTypeDisplayName(qrProvider.currentQRCode!.type)}',
                  style: Theme.of(context).textTheme.bodySmall?.copyWith(
                    color: Colors.grey[600],
                  ),
                ),
            ],
          ),
        );
      },
    );
  }

  String _getTypeDisplayName(QRCodeType type) {
    switch (type) {
      case QRCodeType.text:
        return 'Text';
      case QRCodeType.url:
        return 'URL';
      case QRCodeType.email:
        return 'Email';
      case QRCodeType.phone:
        return 'Phone';
      case QRCodeType.sms:
        return 'SMS';
      case QRCodeType.wifi:
        return 'WiFi';
      case QRCodeType.contact:
        return 'Contact';
      case QRCodeType.location:
        return 'Location';
    }
  }

  void _showSuccessSnackBar(BuildContext context) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: const Row(
          children: [
            Icon(Icons.check_circle, color: Colors.white),
            SizedBox(width: 8),
            Text('QR Code created successfully!'),
          ],
        ),
        backgroundColor: Colors.green,
        behavior: SnackBarBehavior.floating,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(8),
        ),
      ),
    );
  }

  void _showErrorSnackBar(BuildContext context, String error) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Row(
          children: [
            const Icon(Icons.error, color: Colors.white),
            const SizedBox(width: 8),
            Expanded(child: Text(error)),
          ],
        ),
        backgroundColor: Colors.red,
        behavior: SnackBarBehavior.floating,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(8),
        ),
        action: SnackBarAction(
          label: 'Dismiss',
          textColor: Colors.white,
          onPressed: () {
            ScaffoldMessenger.of(context).hideCurrentSnackBar();
          },
        ),
      ),
    );
  }
}

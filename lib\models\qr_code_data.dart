import 'package:flutter/material.dart';
import 'package:pretty_qr_code/pretty_qr_code.dart';

/// Enum for different types of QR code content
enum QRCodeType {
  text,
  url,
  email,
  phone,
  sms,
  wifi,
  contact,
  location,
}

/// Enum for export formats
enum ExportFormat {
  png,
  svg,
  pdf,
}

/// Model class for QR code styling and customization
class QRCodeStyle {
  final Color foregroundColor;
  final Color backgroundColor;
  final PrettyQrDecoration decoration;
  final double borderRadius;
  final bool hasLogo;
  final String? logoPath;
  final double logoSize;
  final String templateName;
  final Gradient? gradient;

  const QRCodeStyle({
    this.foregroundColor = Colors.black,
    this.backgroundColor = Colors.white,
    this.decoration = const PrettyQrDecoration(),
    this.borderRadius = 0.0,
    this.hasLogo = false,
    this.logoPath,
    this.logoSize = 0.2,
    this.templateName = 'default',
    this.gradient,
  });

  QRCodeStyle copyWith({
    Color? foregroundColor,
    Color? backgroundColor,
    PrettyQrDecoration? decoration,
    double? borderRadius,
    bool? hasLogo,
    String? logoPath,
    double? logoSize,
    String? templateName,
    Gradient? gradient,
  }) {
    return QRCodeStyle(
      foregroundColor: foregroundColor ?? this.foregroundColor,
      backgroundColor: backgroundColor ?? this.backgroundColor,
      decoration: decoration ?? this.decoration,
      borderRadius: borderRadius ?? this.borderRadius,
      hasLogo: hasLogo ?? this.hasLogo,
      logoPath: logoPath ?? this.logoPath,
      logoSize: logoSize ?? this.logoSize,
      templateName: templateName ?? this.templateName,
      gradient: gradient ?? this.gradient,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'foregroundColor': foregroundColor.value,
      'backgroundColor': backgroundColor.value,
      'borderRadius': borderRadius,
      'hasLogo': hasLogo,
      'logoPath': logoPath,
      'logoSize': logoSize,
      'templateName': templateName,
    };
  }

  factory QRCodeStyle.fromJson(Map<String, dynamic> json) {
    return QRCodeStyle(
      foregroundColor: Color(json['foregroundColor'] ?? Colors.black.value),
      backgroundColor: Color(json['backgroundColor'] ?? Colors.white.value),
      borderRadius: json['borderRadius']?.toDouble() ?? 0.0,
      hasLogo: json['hasLogo'] ?? false,
      logoPath: json['logoPath'],
      logoSize: json['logoSize']?.toDouble() ?? 0.2,
      templateName: json['templateName'] ?? 'default',
    );
  }
}

/// Model class for QR code data and content
class QRCodeData {
  final String id;
  final String content;
  final QRCodeType type;
  final QRCodeStyle style;
  final DateTime createdAt;
  final String title;
  final String? description;
  final Map<String, dynamic>? metadata;

  const QRCodeData({
    required this.id,
    required this.content,
    required this.type,
    required this.style,
    required this.createdAt,
    required this.title,
    this.description,
    this.metadata,
  });

  QRCodeData copyWith({
    String? id,
    String? content,
    QRCodeType? type,
    QRCodeStyle? style,
    DateTime? createdAt,
    String? title,
    String? description,
    Map<String, dynamic>? metadata,
  }) {
    return QRCodeData(
      id: id ?? this.id,
      content: content ?? this.content,
      type: type ?? this.type,
      style: style ?? this.style,
      createdAt: createdAt ?? this.createdAt,
      title: title ?? this.title,
      description: description ?? this.description,
      metadata: metadata ?? this.metadata,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'content': content,
      'type': type.name,
      'style': style.toJson(),
      'createdAt': createdAt.toIso8601String(),
      'title': title,
      'description': description,
      'metadata': metadata,
    };
  }

  factory QRCodeData.fromJson(Map<String, dynamic> json) {
    return QRCodeData(
      id: json['id'],
      content: json['content'],
      type: QRCodeType.values.firstWhere(
        (e) => e.name == json['type'],
        orElse: () => QRCodeType.text,
      ),
      style: QRCodeStyle.fromJson(json['style']),
      createdAt: DateTime.parse(json['createdAt']),
      title: json['title'],
      description: json['description'],
      metadata: json['metadata'],
    );
  }
}

/// Contact information model for vCard QR codes
class ContactInfo {
  final String firstName;
  final String lastName;
  final String? organization;
  final String? phone;
  final String? email;
  final String? website;
  final String? address;

  const ContactInfo({
    required this.firstName,
    required this.lastName,
    this.organization,
    this.phone,
    this.email,
    this.website,
    this.address,
  });

  String toVCard() {
    final buffer = StringBuffer();
    buffer.writeln('BEGIN:VCARD');
    buffer.writeln('VERSION:3.0');
    buffer.writeln('FN:$firstName $lastName');
    buffer.writeln('N:$lastName;$firstName;;;');
    
    if (organization != null) {
      buffer.writeln('ORG:$organization');
    }
    if (phone != null) {
      buffer.writeln('TEL:$phone');
    }
    if (email != null) {
      buffer.writeln('EMAIL:$email');
    }
    if (website != null) {
      buffer.writeln('URL:$website');
    }
    if (address != null) {
      buffer.writeln('ADR:;;$address;;;;');
    }
    
    buffer.writeln('END:VCARD');
    return buffer.toString();
  }
}

/// WiFi configuration model for WiFi QR codes
class WiFiConfig {
  final String ssid;
  final String password;
  final String security; // WPA, WEP, or nopass
  final bool hidden;

  const WiFiConfig({
    required this.ssid,
    required this.password,
    this.security = 'WPA',
    this.hidden = false,
  });

  String toWiFiString() {
    return 'WIFI:T:$security;S:$ssid;P:$password;H:${hidden ? 'true' : 'false'};;';
  }
}

/// Location model for location QR codes
class LocationInfo {
  final double latitude;
  final double longitude;
  final String? label;

  const LocationInfo({
    required this.latitude,
    required this.longitude,
    this.label,
  });

  String toGeoString() {
    if (label != null) {
      return 'geo:$latitude,$longitude?q=$latitude,$longitude($label)';
    }
    return 'geo:$latitude,$longitude';
  }
}

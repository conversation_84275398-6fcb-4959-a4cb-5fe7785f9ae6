import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:flutter_colorpicker/flutter_colorpicker.dart';
import '../providers/qr_provider.dart';
import '../services/qr_generator_service.dart';
import '../services/file_service.dart';
import '../models/qr_code_data.dart';

/// Widget for customizing QR code appearance and style
class QRCustomizationWidget extends StatefulWidget {
  const QRCustomizationWidget({super.key});

  @override
  State<QRCustomizationWidget> createState() => _QRCustomizationWidgetState();
}

class _QRCustomizationWidgetState extends State<QRCustomizationWidget> {
  @override
  Widget build(BuildContext context) {
    return Consumer<QRProvider>(
      builder: (context, qrProvider, child) {
        return SingleChildScrollView(
          padding: const EdgeInsets.all(16),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.stretch,
            children: [
              // QR Code Preview
              _buildQRPreview(qrProvider),
              const SizedBox(height: 24),

              // Color Customization
              _buildColorCustomization(qrProvider),
              const SizedBox(height: 16),

              // Professional Color Schemes
              _buildColorSchemes(qrProvider),
              const SizedBox(height: 16),

              // Style Options
              _buildStyleOptions(qrProvider),
              const SizedBox(height: 16),

              // Logo Options
              _buildLogoOptions(qrProvider),
              const SizedBox(height: 16),

              // Export Options
              _buildExportOptions(qrProvider),
              const SizedBox(height: 100), // Space for content
            ],
          ),
        );
      },
    );
  }

  Widget _buildQRPreview(QRProvider qrProvider) {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          children: [
            Text(
              'Customization Preview',
              style: Theme.of(
                context,
              ).textTheme.titleMedium?.copyWith(fontWeight: FontWeight.bold),
            ),
            const SizedBox(height: 16),
            Container(
              width: 250,
              height: 250,
              decoration: BoxDecoration(
                border: Border.all(color: Colors.grey.shade300),
                borderRadius: BorderRadius.circular(12),
              ),
              child:
                  qrProvider.currentContent.isNotEmpty
                      ? QRGeneratorService.generateQRCodeWidget(
                        data: qrProvider.currentContent,
                        style: qrProvider.currentStyle,
                        size: 250,
                      )
                      : Center(
                        child: Column(
                          mainAxisAlignment: MainAxisAlignment.center,
                          children: [
                            Icon(
                              Icons.qr_code,
                              size: 64,
                              color: Colors.grey.shade400,
                            ),
                            const SizedBox(height: 8),
                            Text(
                              'Create a QR code first\nto see customization',
                              textAlign: TextAlign.center,
                              style: TextStyle(
                                color: Colors.grey.shade600,
                                fontSize: 14,
                              ),
                            ),
                          ],
                        ),
                      ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildColorCustomization(QRProvider qrProvider) {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Colors',
              style: Theme.of(
                context,
              ).textTheme.titleSmall?.copyWith(fontWeight: FontWeight.bold),
            ),
            const SizedBox(height: 16),
            Row(
              children: [
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        'Foreground',
                        style: Theme.of(context).textTheme.bodyMedium,
                      ),
                      const SizedBox(height: 8),
                      GestureDetector(
                        onTap:
                            () => _showColorPicker(
                              context,
                              qrProvider.currentStyle.foregroundColor,
                              (color) =>
                                  qrProvider.updateForegroundColor(color),
                            ),
                        child: Container(
                          width: double.infinity,
                          height: 50,
                          decoration: BoxDecoration(
                            color: qrProvider.currentStyle.foregroundColor,
                            borderRadius: BorderRadius.circular(8),
                            border: Border.all(color: Colors.grey.shade300),
                          ),
                          child: const Icon(
                            Icons.colorize,
                            color: Colors.white,
                          ),
                        ),
                      ),
                    ],
                  ),
                ),
                const SizedBox(width: 16),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        'Background',
                        style: Theme.of(context).textTheme.bodyMedium,
                      ),
                      const SizedBox(height: 8),
                      GestureDetector(
                        onTap:
                            () => _showColorPicker(
                              context,
                              qrProvider.currentStyle.backgroundColor,
                              (color) =>
                                  qrProvider.updateBackgroundColor(color),
                            ),
                        child: Container(
                          width: double.infinity,
                          height: 50,
                          decoration: BoxDecoration(
                            color: qrProvider.currentStyle.backgroundColor,
                            borderRadius: BorderRadius.circular(8),
                            border: Border.all(color: Colors.grey.shade300),
                          ),
                          child: Icon(
                            Icons.colorize,
                            color:
                                qrProvider.currentStyle.backgroundColor
                                            .computeLuminance() >
                                        0.5
                                    ? Colors.black
                                    : Colors.white,
                          ),
                        ),
                      ),
                    ],
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildColorSchemes(QRProvider qrProvider) {
    final schemes = QRGeneratorService.getProfessionalColorSchemes();

    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Professional Color Schemes',
              style: Theme.of(
                context,
              ).textTheme.titleSmall?.copyWith(fontWeight: FontWeight.bold),
            ),
            const SizedBox(height: 16),
            GridView.builder(
              shrinkWrap: true,
              physics: const NeverScrollableScrollPhysics(),
              gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
                crossAxisCount: 2,
                childAspectRatio: 3,
                crossAxisSpacing: 8,
                mainAxisSpacing: 8,
              ),
              itemCount: schemes.length,
              itemBuilder: (context, index) {
                final scheme = schemes[index];
                return GestureDetector(
                  onTap:
                      () => qrProvider.applyColorScheme({
                        'foreground': scheme['foreground'] as Color,
                        'background': scheme['background'] as Color,
                      }),
                  child: Container(
                    decoration: BoxDecoration(
                      borderRadius: BorderRadius.circular(8),
                      border: Border.all(color: Colors.grey.shade300),
                    ),
                    child: Row(
                      children: [
                        Expanded(
                          child: Container(
                            decoration: BoxDecoration(
                              color: scheme['foreground'],
                              borderRadius: const BorderRadius.only(
                                topLeft: Radius.circular(8),
                                bottomLeft: Radius.circular(8),
                              ),
                            ),
                          ),
                        ),
                        Expanded(
                          flex: 2,
                          child: Container(
                            decoration: BoxDecoration(
                              color: scheme['background'],
                              borderRadius: const BorderRadius.only(
                                topRight: Radius.circular(8),
                                bottomRight: Radius.circular(8),
                              ),
                            ),
                            child: Center(
                              child: Text(
                                scheme['name'] as String,
                                style: TextStyle(
                                  fontSize: 12,
                                  fontWeight: FontWeight.w500,
                                  color:
                                      (scheme['background'] as Color)
                                                  .computeLuminance() >
                                              0.5
                                          ? Colors.black
                                          : Colors.white,
                                ),
                              ),
                            ),
                          ),
                        ),
                      ],
                    ),
                  ),
                );
              },
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildStyleOptions(QRProvider qrProvider) {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Style Options',
              style: Theme.of(
                context,
              ).textTheme.titleSmall?.copyWith(fontWeight: FontWeight.bold),
            ),
            const SizedBox(height: 16),
            Text(
              'Border Radius: ${qrProvider.currentStyle.borderRadius.toInt()}',
              style: Theme.of(context).textTheme.bodyMedium,
            ),
            Slider(
              value: qrProvider.currentStyle.borderRadius,
              min: 0,
              max: 20,
              divisions: 20,
              onChanged: (value) {
                qrProvider.updateBorderRadius(value);
              },
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildLogoOptions(QRProvider qrProvider) {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Logo Options',
              style: Theme.of(
                context,
              ).textTheme.titleSmall?.copyWith(fontWeight: FontWeight.bold),
            ),
            const SizedBox(height: 16),
            SwitchListTile(
              title: const Text('Add Logo'),
              subtitle: const Text('Add a logo to the center of your QR code'),
              value: qrProvider.currentStyle.hasLogo,
              onChanged: (value) {
                if (value) {
                  _pickLogo(qrProvider);
                } else {
                  qrProvider.toggleLogo(false);
                }
              },
            ),
            if (qrProvider.currentStyle.hasLogo) ...[
              const SizedBox(height: 16),
              Text(
                'Logo Size: ${(qrProvider.currentStyle.logoSize * 100).toInt()}%',
                style: Theme.of(context).textTheme.bodyMedium,
              ),
              Slider(
                value: qrProvider.currentStyle.logoSize,
                min: 0.1,
                max: 0.3,
                divisions: 20,
                onChanged: (value) {
                  qrProvider.updateLogoSize(value);
                },
              ),
              ElevatedButton.icon(
                onPressed: () => _pickLogo(qrProvider),
                icon: const Icon(Icons.image),
                label: const Text('Change Logo'),
              ),
            ],
          ],
        ),
      ),
    );
  }

  Widget _buildExportOptions(QRProvider qrProvider) {
    if (qrProvider.currentQRCode == null) {
      return const SizedBox.shrink();
    }

    final sizeRecommendations = QRGeneratorService.getSizeRecommendations();

    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Export Options',
              style: Theme.of(
                context,
              ).textTheme.titleSmall?.copyWith(fontWeight: FontWeight.bold),
            ),
            const SizedBox(height: 16),
            Text(
              'Export Sizes',
              style: Theme.of(
                context,
              ).textTheme.bodyMedium?.copyWith(fontWeight: FontWeight.w500),
            ),
            const SizedBox(height: 8),
            Wrap(
              spacing: 8,
              runSpacing: 8,
              children:
                  sizeRecommendations.entries.map((entry) {
                    return ActionChip(
                      label: Text('${entry.key}\n${entry.value}px'),
                      onPressed: () => _exportQRCode(qrProvider, entry.value),
                    );
                  }).toList(),
            ),
            const SizedBox(height: 16),
            Row(
              children: [
                Expanded(
                  child: ElevatedButton.icon(
                    onPressed: () => _shareQRCode(qrProvider),
                    icon: const Icon(Icons.share),
                    label: const Text('Share'),
                  ),
                ),
                const SizedBox(width: 8),
                Expanded(
                  child: ElevatedButton.icon(
                    onPressed: () => _exportQRCode(qrProvider, 512),
                    icon: const Icon(Icons.download),
                    label: const Text('Download'),
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  void _showColorPicker(
    BuildContext context,
    Color currentColor,
    Function(Color) onColorChanged,
  ) {
    showDialog(
      context: context,
      builder: (context) {
        return AlertDialog(
          title: const Text('Pick a Color'),
          content: SingleChildScrollView(
            child: ColorPicker(
              pickerColor: currentColor,
              onColorChanged: onColorChanged,
              pickerAreaHeightPercent: 0.8,
            ),
          ),
          actions: [
            TextButton(
              onPressed: () => Navigator.of(context).pop(),
              child: const Text('Done'),
            ),
          ],
        );
      },
    );
  }

  Future<void> _pickLogo(QRProvider qrProvider) async {
    try {
      final logoPath = await FileService.pickLogoImage();
      if (logoPath != null) {
        qrProvider.toggleLogo(true, logoPath: logoPath);
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Failed to pick logo: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  Future<void> _exportQRCode(QRProvider qrProvider, int size) async {
    if (qrProvider.currentQRCode == null) return;

    try {
      final filePath = await qrProvider.exportQRCode(
        qrCode: qrProvider.currentQRCode!,
        format: ExportFormat.png,
        size: size,
      );

      if (filePath != null && mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('QR code saved to: $filePath'),
            backgroundColor: Colors.green,
          ),
        );
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Failed to export QR code: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  Future<void> _shareQRCode(QRProvider qrProvider) async {
    if (qrProvider.currentQRCode == null) return;

    try {
      await qrProvider.shareQRCode(
        qrCode: qrProvider.currentQRCode!,
        format: ExportFormat.png,
        size: 512,
      );
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Failed to share QR code: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }
}

import 'package:flutter/material.dart';
import 'package:uuid/uuid.dart';
import '../models/qr_code_data.dart';
import '../services/qr_generator_service.dart';
import '../services/file_service.dart';

/// Provider class for managing QR code state and operations
class QRProvider extends ChangeNotifier {
  final List<QRCodeData> _qrCodes = [];
  QRCodeData? _currentQRCode;
  QRCodeStyle _currentStyle = const QRCodeStyle();
  QRCodeType _currentType = QRCodeType.text;
  String _currentContent = '';
  String _currentTitle = '';
  String _currentDescription = '';
  bool _isLoading = false;
  String? _error;

  // Getters
  List<QRCodeData> get qrCodes => List.unmodifiable(_qrCodes);
  QRCodeData? get currentQRCode => _currentQRCode;
  QRCodeStyle get currentStyle => _currentStyle;
  QRCodeType get currentType => _currentType;
  String get currentContent => _currentContent;
  String get currentTitle => _currentTitle;
  String get currentDescription => _currentDescription;
  bool get isLoading => _isLoading;
  String? get error => _error;

  /// Set loading state
  void _setLoading(bool loading) {
    _isLoading = loading;
    notifyListeners();
  }

  /// Set error message
  void _setError(String? error) {
    _error = error;
    notifyListeners();
  }

  /// Clear error
  void clearError() {
    _error = null;
    notifyListeners();
  }

  /// Update current QR code content
  void updateContent(String content) {
    _currentContent = content;
    _updateCurrentQRCode();
    notifyListeners();
  }

  /// Update current QR code title
  void updateTitle(String title) {
    _currentTitle = title;
    _updateCurrentQRCode();
    notifyListeners();
  }

  /// Update current QR code description
  void updateDescription(String description) {
    _currentDescription = description;
    _updateCurrentQRCode();
    notifyListeners();
  }

  /// Update current QR code type
  void updateType(QRCodeType type) {
    _currentType = type;
    _updateCurrentQRCode();
    notifyListeners();
  }

  /// Update current QR code style
  void updateStyle(QRCodeStyle style) {
    _currentStyle = style;
    _updateCurrentQRCode();
    notifyListeners();
  }

  /// Update foreground color
  void updateForegroundColor(Color color) {
    _currentStyle = _currentStyle.copyWith(foregroundColor: color);
    _updateCurrentQRCode();
    notifyListeners();
  }

  /// Update background color
  void updateBackgroundColor(Color color) {
    _currentStyle = _currentStyle.copyWith(backgroundColor: color);
    _updateCurrentQRCode();
    notifyListeners();
  }

  /// Update border radius
  void updateBorderRadius(double radius) {
    _currentStyle = _currentStyle.copyWith(borderRadius: radius);
    _updateCurrentQRCode();
    notifyListeners();
  }

  /// Toggle logo
  void toggleLogo(bool hasLogo, {String? logoPath}) {
    _currentStyle = _currentStyle.copyWith(
      hasLogo: hasLogo,
      logoPath: logoPath,
    );
    _updateCurrentQRCode();
    notifyListeners();
  }

  /// Update logo size
  void updateLogoSize(double size) {
    _currentStyle = _currentStyle.copyWith(logoSize: size);
    _updateCurrentQRCode();
    notifyListeners();
  }

  /// Apply professional color scheme
  void applyColorScheme(Map<String, Color> scheme) {
    _currentStyle = _currentStyle.copyWith(
      foregroundColor: scheme['foreground'],
      backgroundColor: scheme['background'],
    );
    _updateCurrentQRCode();
    notifyListeners();
  }

  /// Create new QR code
  Future<void> createQRCode() async {
    if (_currentContent.isEmpty || _currentTitle.isEmpty) {
      _setError('Content and title are required');
      return;
    }

    if (!QRGeneratorService.validateQRData(_currentContent, _currentType)) {
      _setError('Invalid content for selected QR code type');
      return;
    }

    _setLoading(true);
    _setError(null);

    try {
      final formattedContent = QRGeneratorService.formatQRData(
        _currentContent,
        _currentType,
      );

      final qrCode = QRCodeData(
        id: const Uuid().v4(),
        content: formattedContent,
        type: _currentType,
        style: _currentStyle,
        createdAt: DateTime.now(),
        title: _currentTitle,
        description: _currentDescription.isNotEmpty ? _currentDescription : null,
      );

      _qrCodes.insert(0, qrCode);
      _currentQRCode = qrCode;
      
      notifyListeners();
    } catch (e) {
      _setError('Failed to create QR code: $e');
    } finally {
      _setLoading(false);
    }
  }

  /// Update existing QR code
  Future<void> updateQRCode(String id) async {
    final index = _qrCodes.indexWhere((qr) => qr.id == id);
    if (index == -1) {
      _setError('QR code not found');
      return;
    }

    if (_currentContent.isEmpty || _currentTitle.isEmpty) {
      _setError('Content and title are required');
      return;
    }

    if (!QRGeneratorService.validateQRData(_currentContent, _currentType)) {
      _setError('Invalid content for selected QR code type');
      return;
    }

    _setLoading(true);
    _setError(null);

    try {
      final formattedContent = QRGeneratorService.formatQRData(
        _currentContent,
        _currentType,
      );

      final updatedQRCode = _qrCodes[index].copyWith(
        content: formattedContent,
        type: _currentType,
        style: _currentStyle,
        title: _currentTitle,
        description: _currentDescription.isNotEmpty ? _currentDescription : null,
      );

      _qrCodes[index] = updatedQRCode;
      _currentQRCode = updatedQRCode;
      
      notifyListeners();
    } catch (e) {
      _setError('Failed to update QR code: $e');
    } finally {
      _setLoading(false);
    }
  }

  /// Delete QR code
  void deleteQRCode(String id) {
    _qrCodes.removeWhere((qr) => qr.id == id);
    if (_currentQRCode?.id == id) {
      _currentQRCode = null;
    }
    notifyListeners();
  }

  /// Select QR code for editing
  void selectQRCode(QRCodeData qrCode) {
    _currentQRCode = qrCode;
    _currentContent = qrCode.content;
    _currentTitle = qrCode.title;
    _currentDescription = qrCode.description ?? '';
    _currentType = qrCode.type;
    _currentStyle = qrCode.style;
    notifyListeners();
  }

  /// Clear current selection
  void clearSelection() {
    _currentQRCode = null;
    _currentContent = '';
    _currentTitle = '';
    _currentDescription = '';
    _currentType = QRCodeType.text;
    _currentStyle = const QRCodeStyle();
    notifyListeners();
  }

  /// Export QR code
  Future<String?> exportQRCode({
    required QRCodeData qrCode,
    required ExportFormat format,
    int size = 512,
  }) async {
    _setLoading(true);
    _setError(null);

    try {
      final filePath = await FileService.saveQRCodeToDevice(
        qrData: qrCode,
        format: format,
        size: size,
      );
      return filePath;
    } catch (e) {
      _setError('Failed to export QR code: $e');
      return null;
    } finally {
      _setLoading(false);
    }
  }

  /// Share QR code
  Future<void> shareQRCode({
    required QRCodeData qrCode,
    required ExportFormat format,
    int size = 512,
  }) async {
    _setLoading(true);
    _setError(null);

    try {
      await FileService.shareQRCode(
        qrData: qrCode,
        format: format,
        size: size,
      );
    } catch (e) {
      _setError('Failed to share QR code: $e');
    } finally {
      _setLoading(false);
    }
  }

  /// Update current QR code if one is selected
  void _updateCurrentQRCode() {
    if (_currentContent.isNotEmpty && _currentTitle.isNotEmpty) {
      final formattedContent = QRGeneratorService.formatQRData(
        _currentContent,
        _currentType,
      );

      _currentQRCode = QRCodeData(
        id: _currentQRCode?.id ?? const Uuid().v4(),
        content: formattedContent,
        type: _currentType,
        style: _currentStyle,
        createdAt: _currentQRCode?.createdAt ?? DateTime.now(),
        title: _currentTitle,
        description: _currentDescription.isNotEmpty ? _currentDescription : null,
      );
    }
  }

  /// Get QR codes by type
  List<QRCodeData> getQRCodesByType(QRCodeType type) {
    return _qrCodes.where((qr) => qr.type == type).toList();
  }

  /// Search QR codes
  List<QRCodeData> searchQRCodes(String query) {
    if (query.isEmpty) return _qrCodes;
    
    final lowercaseQuery = query.toLowerCase();
    return _qrCodes.where((qr) =>
      qr.title.toLowerCase().contains(lowercaseQuery) ||
      qr.content.toLowerCase().contains(lowercaseQuery) ||
      (qr.description?.toLowerCase().contains(lowercaseQuery) ?? false)
    ).toList();
  }
}

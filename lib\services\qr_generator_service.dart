import 'dart:typed_data';
import 'dart:ui' as ui;
import 'package:flutter/material.dart';
import 'package:flutter/rendering.dart';
import 'package:pretty_qr_code/pretty_qr_code.dart';
import 'package:qr/qr.dart';
import 'package:image/image.dart' as img;
import 'package:pdf/pdf.dart';
import 'package:pdf/widgets.dart' as pw;
import '../models/qr_code_data.dart';

/// Service class for generating QR codes with various customization options
class QRGeneratorService {
  static const int defaultSize = 512;
  static const int highResSize = 1024;
  static const int printSize = 2048;

  /// Generate QR code image as bytes
  static Future<Uint8List> generateQRCodeBytes({
    required String data,
    required QRCodeStyle style,
    int size = defaultSize,
    ExportFormat format = ExportFormat.png,
  }) async {
    try {
      final qrCode = QrCode.fromData(
        data: data,
        errorCorrectLevel: QrErrorCorrectLevel.H,
      );

      final qrImage = QrImage(qrCode);

      // Create decoration with style
      final decoration = _createDecoration(style);

      switch (format) {
        case ExportFormat.png:
          final bytes = await qrImage.toImageAsBytes(
            size: size,
            decoration: decoration,
          );
          return bytes?.buffer.asUint8List() ?? Uint8List(0);
        case ExportFormat.svg:
          // For SVG, we'll generate PNG and convert (simplified approach)
          final bytes = await qrImage.toImageAsBytes(
            size: size,
            decoration: decoration,
          );
          return bytes?.buffer.asUint8List() ?? Uint8List(0);
        case ExportFormat.pdf:
          return await _generatePDF(qrImage, decoration, size);
      }
    } catch (e) {
      throw Exception('Failed to generate QR code: $e');
    }
  }

  /// Generate QR code widget for display
  static Widget generateQRCodeWidget({
    required String data,
    required QRCodeStyle style,
    double size = 200.0,
  }) {
    try {
      final decoration = _createDecoration(style);

      return Container(
        width: size,
        height: size,
        decoration: BoxDecoration(
          color: style.backgroundColor,
          borderRadius: BorderRadius.circular(style.borderRadius),
          boxShadow: [
            BoxShadow(
              color: Colors.black.withOpacity(0.1),
              blurRadius: 8,
              offset: const Offset(0, 4),
            ),
          ],
        ),
        child: ClipRRect(
          borderRadius: BorderRadius.circular(style.borderRadius),
          child: PrettyQrView.data(data: data, decoration: decoration),
        ),
      );
    } catch (e) {
      return Container(
        width: size,
        height: size,
        decoration: BoxDecoration(
          color: Colors.red.withOpacity(0.1),
          borderRadius: BorderRadius.circular(style.borderRadius),
          border: Border.all(color: Colors.red),
        ),
        child: const Center(
          child: Icon(Icons.error, color: Colors.red, size: 48),
        ),
      );
    }
  }

  /// Create PrettyQrDecoration from QRCodeStyle
  static PrettyQrDecoration _createDecoration(QRCodeStyle style) {
    return PrettyQrDecoration(
      shape: PrettyQrSmoothSymbol(color: style.foregroundColor),
      image:
          style.hasLogo && style.logoPath != null
              ? PrettyQrDecorationImage(
                image: AssetImage(style.logoPath!),
                scale: style.logoSize,
              )
              : null,
    );
  }

  /// Generate PDF with QR code
  static Future<Uint8List> _generatePDF(
    QrImage qrImage,
    PrettyQrDecoration decoration,
    int size,
  ) async {
    final pdf = pw.Document();

    // Generate PNG first
    final pngBytes = await qrImage.toImageAsBytes(
      size: size,
      decoration: decoration,
    );

    if (pngBytes != null) {
      pdf.addPage(
        pw.Page(
          pageFormat: PdfPageFormat.a4,
          build: (pw.Context context) {
            return pw.Center(
              child: pw.Image(
                pw.MemoryImage(pngBytes.buffer.asUint8List()),
                width: 300,
                height: 300,
              ),
            );
          },
        ),
      );
    }

    return await pdf.save();
  }

  /// Validate QR code data based on type
  static bool validateQRData(String data, QRCodeType type) {
    if (data.isEmpty) return false;

    switch (type) {
      case QRCodeType.url:
        return Uri.tryParse(data) != null &&
            (data.startsWith('http://') || data.startsWith('https://'));
      case QRCodeType.email:
        return RegExp(r'^[\w-\.]+@([\w-]+\.)+[\w-]{2,4}$').hasMatch(data);
      case QRCodeType.phone:
        return RegExp(r'^\+?[\d\s\-\(\)]+$').hasMatch(data);
      case QRCodeType.text:
      case QRCodeType.sms:
      case QRCodeType.wifi:
      case QRCodeType.contact:
      case QRCodeType.location:
        return true;
    }
  }

  /// Format data based on QR code type
  static String formatQRData(
    String data,
    QRCodeType type, {
    Map<String, dynamic>? metadata,
  }) {
    switch (type) {
      case QRCodeType.email:
        final subject = metadata?['subject'] ?? '';
        final body = metadata?['body'] ?? '';
        if (subject.isNotEmpty || body.isNotEmpty) {
          return 'mailto:$data?subject=${Uri.encodeComponent(subject)}&body=${Uri.encodeComponent(body)}';
        }
        return 'mailto:$data';

      case QRCodeType.phone:
        return 'tel:$data';

      case QRCodeType.sms:
        final message = metadata?['message'] ?? '';
        if (message.isNotEmpty) {
          return 'sms:$data?body=${Uri.encodeComponent(message)}';
        }
        return 'sms:$data';

      case QRCodeType.url:
        if (!data.startsWith('http://') && !data.startsWith('https://')) {
          return 'https://$data';
        }
        return data;

      case QRCodeType.wifi:
      case QRCodeType.contact:
      case QRCodeType.location:
      case QRCodeType.text:
        return data;
    }
  }

  /// Get suggested filename for export
  static String getSuggestedFilename(QRCodeData qrData, ExportFormat format) {
    final timestamp = DateTime.now().millisecondsSinceEpoch;
    final extension = format.name;
    final sanitizedTitle =
        qrData.title.replaceAll(RegExp(r'[^\w\s-]'), '').trim();

    if (sanitizedTitle.isNotEmpty) {
      return '${sanitizedTitle}_$timestamp.$extension';
    }

    return 'qr_code_$timestamp.$extension';
  }

  /// Get QR code size recommendations
  static Map<String, int> getSizeRecommendations() {
    return {
      'Small (Web)': 256,
      'Medium (Mobile)': 512,
      'Large (Print)': 1024,
      'Extra Large (Professional)': 2048,
    };
  }

  /// Get professional color schemes
  static List<Map<String, dynamic>> getProfessionalColorSchemes() {
    return [
      {
        'name': 'Classic',
        'foreground': Colors.black,
        'background': Colors.white,
      },
      {
        'name': 'Corporate Blue',
        'foreground': const Color(0xFF1565C0),
        'background': const Color(0xFFF5F5F5),
      },
      {
        'name': 'Modern Dark',
        'foreground': const Color(0xFF212121),
        'background': const Color(0xFFFAFAFA),
      },
      {
        'name': 'Professional Green',
        'foreground': const Color(0xFF2E7D32),
        'background': Colors.white,
      },
      {
        'name': 'Elegant Purple',
        'foreground': const Color(0xFF6A1B9A),
        'background': const Color(0xFFF3E5F5),
      },
      {
        'name': 'Tech Orange',
        'foreground': const Color(0xFFE65100),
        'background': const Color(0xFFFFF3E0),
      },
    ];
  }
}

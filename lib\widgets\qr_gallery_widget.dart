import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../providers/qr_provider.dart';
import '../models/qr_code_data.dart';
import '../services/qr_generator_service.dart';

/// Widget for displaying and managing saved QR codes
class QRGalleryWidget extends StatefulWidget {
  const QRGalleryWidget({super.key});

  @override
  State<QRGalleryWidget> createState() => _QRGalleryWidgetState();
}

class _QRGalleryWidgetState extends State<QRGalleryWidget> {
  final _searchController = TextEditingController();
  String _searchQuery = '';
  QRCodeType? _filterType;

  @override
  void dispose() {
    _searchController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Consumer<QRProvider>(
      builder: (context, qrProvider, child) {
        final filteredQRCodes = _getFilteredQRCodes(qrProvider);

        return Column(
          children: [
            // Search and Filter Bar
            _buildSearchAndFilter(qrProvider),
            
            // QR Codes Grid
            Expanded(
              child: filteredQRCodes.isEmpty
                  ? _buildEmptyState()
                  : _buildQRCodesGrid(filteredQRCodes, qrProvider),
            ),
          ],
        );
      },
    );
  }

  Widget _buildSearchAndFilter(QRProvider qrProvider) {
    return Container(
      padding: const EdgeInsets.all(16),
      child: Column(
        children: [
          // Search Bar
          TextField(
            controller: _searchController,
            decoration: InputDecoration(
              hintText: 'Search QR codes...',
              prefixIcon: const Icon(Icons.search),
              suffixIcon: _searchQuery.isNotEmpty
                  ? IconButton(
                      onPressed: () {
                        _searchController.clear();
                        setState(() {
                          _searchQuery = '';
                        });
                      },
                      icon: const Icon(Icons.clear),
                    )
                  : null,
            ),
            onChanged: (value) {
              setState(() {
                _searchQuery = value;
              });
            },
          ),
          const SizedBox(height: 12),
          
          // Filter Chips
          SingleChildScrollView(
            scrollDirection: Axis.horizontal,
            child: Row(
              children: [
                FilterChip(
                  label: const Text('All'),
                  selected: _filterType == null,
                  onSelected: (selected) {
                    setState(() {
                      _filterType = null;
                    });
                  },
                ),
                const SizedBox(width: 8),
                ...QRCodeType.values.map((type) {
                  final count = qrProvider.getQRCodesByType(type).length;
                  return Padding(
                    padding: const EdgeInsets.only(right: 8),
                    child: FilterChip(
                      label: Text('${_getTypeDisplayName(type)} ($count)'),
                      selected: _filterType == type,
                      onSelected: (selected) {
                        setState(() {
                          _filterType = selected ? type : null;
                        });
                      },
                    ),
                  );
                }),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildEmptyState() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.qr_code_2,
            size: 64,
            color: Colors.grey.shade400,
          ),
          const SizedBox(height: 16),
          Text(
            _searchQuery.isNotEmpty || _filterType != null
                ? 'No QR codes match your search'
                : 'No QR codes created yet',
            style: Theme.of(context).textTheme.titleMedium?.copyWith(
              color: Colors.grey.shade600,
            ),
          ),
          const SizedBox(height: 8),
          Text(
            _searchQuery.isNotEmpty || _filterType != null
                ? 'Try adjusting your search or filters'
                : 'Create your first QR code in the Generate tab',
            style: Theme.of(context).textTheme.bodyMedium?.copyWith(
              color: Colors.grey.shade500,
            ),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }

  Widget _buildQRCodesGrid(List<QRCodeData> qrCodes, QRProvider qrProvider) {
    return GridView.builder(
      padding: const EdgeInsets.all(16),
      gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
        crossAxisCount: 2,
        childAspectRatio: 0.8,
        crossAxisSpacing: 16,
        mainAxisSpacing: 16,
      ),
      itemCount: qrCodes.length,
      itemBuilder: (context, index) {
        final qrCode = qrCodes[index];
        return _buildQRCodeCard(qrCode, qrProvider);
      },
    );
  }

  Widget _buildQRCodeCard(QRCodeData qrCode, QRProvider qrProvider) {
    return Card(
      clipBehavior: Clip.antiAlias,
      child: InkWell(
        onTap: () => _showQRCodeDetails(qrCode, qrProvider),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.stretch,
          children: [
            // QR Code Preview
            Expanded(
              flex: 3,
              child: Container(
                padding: const EdgeInsets.all(8),
                child: QRGeneratorService.generateQRCodeWidget(
                  data: qrCode.content,
                  style: qrCode.style,
                  size: 120,
                ),
              ),
            ),
            
            // QR Code Info
            Expanded(
              flex: 2,
              child: Padding(
                padding: const EdgeInsets.all(12),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      qrCode.title,
                      style: Theme.of(context).textTheme.titleSmall?.copyWith(
                        fontWeight: FontWeight.bold,
                      ),
                      maxLines: 1,
                      overflow: TextOverflow.ellipsis,
                    ),
                    const SizedBox(height: 4),
                    Row(
                      children: [
                        Icon(
                          _getTypeIcon(qrCode.type),
                          size: 14,
                          color: Colors.grey.shade600,
                        ),
                        const SizedBox(width: 4),
                        Expanded(
                          child: Text(
                            _getTypeDisplayName(qrCode.type),
                            style: Theme.of(context).textTheme.bodySmall?.copyWith(
                              color: Colors.grey.shade600,
                            ),
                            maxLines: 1,
                            overflow: TextOverflow.ellipsis,
                          ),
                        ),
                      ],
                    ),
                    const SizedBox(height: 4),
                    Text(
                      _formatDate(qrCode.createdAt),
                      style: Theme.of(context).textTheme.bodySmall?.copyWith(
                        color: Colors.grey.shade500,
                      ),
                    ),
                  ],
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  void _showQRCodeDetails(QRCodeData qrCode, QRProvider qrProvider) {
    showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      shape: const RoundedRectangleBorder(
        borderRadius: BorderRadius.vertical(top: Radius.circular(20)),
      ),
      builder: (context) => DraggableScrollableSheet(
        initialChildSize: 0.7,
        maxChildSize: 0.9,
        minChildSize: 0.5,
        expand: false,
        builder: (context, scrollController) {
          return SingleChildScrollView(
            controller: scrollController,
            child: Padding(
              padding: const EdgeInsets.all(24),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.stretch,
                children: [
                  // Handle
                  Center(
                    child: Container(
                      width: 40,
                      height: 4,
                      decoration: BoxDecoration(
                        color: Colors.grey.shade300,
                        borderRadius: BorderRadius.circular(2),
                      ),
                    ),
                  ),
                  const SizedBox(height: 24),
                  
                  // QR Code Preview
                  Center(
                    child: Container(
                      width: 200,
                      height: 200,
                      decoration: BoxDecoration(
                        border: Border.all(color: Colors.grey.shade300),
                        borderRadius: BorderRadius.circular(12),
                      ),
                      child: QRGeneratorService.generateQRCodeWidget(
                        data: qrCode.content,
                        style: qrCode.style,
                        size: 200,
                      ),
                    ),
                  ),
                  const SizedBox(height: 24),
                  
                  // QR Code Details
                  _buildDetailRow('Title', qrCode.title),
                  _buildDetailRow('Type', _getTypeDisplayName(qrCode.type)),
                  _buildDetailRow('Content', qrCode.content),
                  if (qrCode.description != null)
                    _buildDetailRow('Description', qrCode.description!),
                  _buildDetailRow('Created', _formatDate(qrCode.createdAt)),
                  
                  const SizedBox(height: 24),
                  
                  // Action Buttons
                  Row(
                    children: [
                      Expanded(
                        child: ElevatedButton.icon(
                          onPressed: () {
                            Navigator.pop(context);
                            qrProvider.selectQRCode(qrCode);
                          },
                          icon: const Icon(Icons.edit),
                          label: const Text('Edit'),
                        ),
                      ),
                      const SizedBox(width: 8),
                      Expanded(
                        child: ElevatedButton.icon(
                          onPressed: () async {
                            Navigator.pop(context);
                            await qrProvider.shareQRCode(
                              qrCode: qrCode,
                              format: ExportFormat.png,
                            );
                          },
                          icon: const Icon(Icons.share),
                          label: const Text('Share'),
                        ),
                      ),
                      const SizedBox(width: 8),
                      IconButton(
                        onPressed: () {
                          Navigator.pop(context);
                          _showDeleteConfirmation(qrCode, qrProvider);
                        },
                        icon: const Icon(Icons.delete),
                        color: Colors.red,
                      ),
                    ],
                  ),
                ],
              ),
            ),
          );
        },
      ),
    );
  }

  Widget _buildDetailRow(String label, String value) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 8),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            label,
            style: Theme.of(context).textTheme.bodySmall?.copyWith(
              fontWeight: FontWeight.bold,
              color: Colors.grey.shade600,
            ),
          ),
          const SizedBox(height: 4),
          Text(
            value,
            style: Theme.of(context).textTheme.bodyMedium,
          ),
        ],
      ),
    );
  }

  void _showDeleteConfirmation(QRCodeData qrCode, QRProvider qrProvider) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Delete QR Code'),
        content: Text('Are you sure you want to delete "${qrCode.title}"?'),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('Cancel'),
          ),
          TextButton(
            onPressed: () {
              Navigator.pop(context);
              qrProvider.deleteQRCode(qrCode.id);
              ScaffoldMessenger.of(context).showSnackBar(
                const SnackBar(
                  content: Text('QR code deleted'),
                  backgroundColor: Colors.green,
                ),
              );
            },
            child: const Text('Delete'),
            style: TextButton.styleFrom(foregroundColor: Colors.red),
          ),
        ],
      ),
    );
  }

  List<QRCodeData> _getFilteredQRCodes(QRProvider qrProvider) {
    List<QRCodeData> qrCodes = qrProvider.qrCodes;

    // Apply type filter
    if (_filterType != null) {
      qrCodes = qrProvider.getQRCodesByType(_filterType!);
    }

    // Apply search filter
    if (_searchQuery.isNotEmpty) {
      qrCodes = qrProvider.searchQRCodes(_searchQuery);
    }

    return qrCodes;
  }

  String _getTypeDisplayName(QRCodeType type) {
    switch (type) {
      case QRCodeType.text: return 'Text';
      case QRCodeType.url: return 'URL';
      case QRCodeType.email: return 'Email';
      case QRCodeType.phone: return 'Phone';
      case QRCodeType.sms: return 'SMS';
      case QRCodeType.wifi: return 'WiFi';
      case QRCodeType.contact: return 'Contact';
      case QRCodeType.location: return 'Location';
    }
  }

  IconData _getTypeIcon(QRCodeType type) {
    switch (type) {
      case QRCodeType.text: return Icons.text_fields;
      case QRCodeType.url: return Icons.link;
      case QRCodeType.email: return Icons.email;
      case QRCodeType.phone: return Icons.phone;
      case QRCodeType.sms: return Icons.sms;
      case QRCodeType.wifi: return Icons.wifi;
      case QRCodeType.contact: return Icons.contact_page;
      case QRCodeType.location: return Icons.location_on;
    }
  }

  String _formatDate(DateTime date) {
    final now = DateTime.now();
    final difference = now.difference(date);

    if (difference.inDays > 0) {
      return '${difference.inDays} day${difference.inDays > 1 ? 's' : ''} ago';
    } else if (difference.inHours > 0) {
      return '${difference.inHours} hour${difference.inHours > 1 ? 's' : ''} ago';
    } else if (difference.inMinutes > 0) {
      return '${difference.inMinutes} minute${difference.inMinutes > 1 ? 's' : ''} ago';
    } else {
      return 'Just now';
    }
  }
}

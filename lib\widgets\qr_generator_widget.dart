import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../providers/qr_provider.dart';
import '../models/qr_code_data.dart';
import '../services/qr_generator_service.dart';

/// Widget for generating QR codes with input forms
class QRGeneratorWidget extends StatefulWidget {
  const QRGeneratorWidget({super.key});

  @override
  State<QRGeneratorWidget> createState() => _QRGeneratorWidgetState();
}

class _QRGeneratorWidgetState extends State<QRGeneratorWidget> {
  final _formKey = GlobalKey<FormState>();
  final _titleController = TextEditingController();
  final _contentController = TextEditingController();
  final _descriptionController = TextEditingController();

  // Additional controllers for specific types
  final _emailSubjectController = TextEditingController();
  final _emailBodyController = TextEditingController();
  final _smsMessageController = TextEditingController();

  @override
  void dispose() {
    _titleController.dispose();
    _contentController.dispose();
    _descriptionController.dispose();
    _emailSubjectController.dispose();
    _emailBodyController.dispose();
    _smsMessageController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Consumer<QRProvider>(
      builder: (context, qrProvider, child) {
        return SingleChildScrollView(
          padding: const EdgeInsets.all(16),
          child: Form(
            key: _formKey,
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.stretch,
              children: [
                // QR Code Preview
                _buildQRPreview(qrProvider),
                const SizedBox(height: 24),

                // QR Code Type Selector
                _buildTypeSelector(qrProvider),
                const SizedBox(height: 16),

                // Title Input
                _buildTitleInput(qrProvider),
                const SizedBox(height: 16),

                // Content Input (varies by type)
                _buildContentInput(qrProvider),
                const SizedBox(height: 16),

                // Additional fields based on type
                ..._buildAdditionalFields(qrProvider),

                // Description Input
                _buildDescriptionInput(qrProvider),
                const SizedBox(height: 24),

                // Quick Templates
                _buildQuickTemplates(qrProvider),
                const SizedBox(height: 100), // Space for FAB
              ],
            ),
          ),
        );
      },
    );
  }

  Widget _buildQRPreview(QRProvider qrProvider) {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          children: [
            Text(
              'QR Code Preview',
              style: Theme.of(
                context,
              ).textTheme.titleMedium?.copyWith(fontWeight: FontWeight.bold),
            ),
            const SizedBox(height: 16),
            Container(
              width: 200,
              height: 200,
              decoration: BoxDecoration(
                border: Border.all(color: Colors.grey.shade300),
                borderRadius: BorderRadius.circular(12),
              ),
              child:
                  qrProvider.currentContent.isNotEmpty
                      ? QRGeneratorService.generateQRCodeWidget(
                        data: qrProvider.currentContent,
                        style: qrProvider.currentStyle,
                        size: 200,
                      )
                      : Center(
                        child: Column(
                          mainAxisAlignment: MainAxisAlignment.center,
                          children: [
                            Icon(
                              Icons.qr_code,
                              size: 48,
                              color: Colors.grey.shade400,
                            ),
                            const SizedBox(height: 8),
                            Text(
                              'Enter content to\ngenerate QR code',
                              textAlign: TextAlign.center,
                              style: TextStyle(
                                color: Colors.grey.shade600,
                                fontSize: 12,
                              ),
                            ),
                          ],
                        ),
                      ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildTypeSelector(QRProvider qrProvider) {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'QR Code Type',
              style: Theme.of(
                context,
              ).textTheme.titleSmall?.copyWith(fontWeight: FontWeight.bold),
            ),
            const SizedBox(height: 12),
            Wrap(
              spacing: 8,
              runSpacing: 8,
              children:
                  QRCodeType.values.map((type) {
                    final isSelected = qrProvider.currentType == type;
                    return FilterChip(
                      label: Text(_getTypeDisplayName(type)),
                      selected: isSelected,
                      onSelected: (selected) {
                        if (selected) {
                          qrProvider.updateType(type);
                          _clearControllers();
                        }
                      },
                      avatar: Icon(_getTypeIcon(type), size: 16),
                    );
                  }).toList(),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildTitleInput(QRProvider qrProvider) {
    return TextFormField(
      controller: _titleController,
      decoration: const InputDecoration(
        labelText: 'Title *',
        hintText: 'Enter a title for your QR code',
        prefixIcon: Icon(Icons.title),
      ),
      validator: (value) {
        if (value == null || value.trim().isEmpty) {
          return 'Title is required';
        }
        return null;
      },
      onChanged: (value) {
        qrProvider.updateTitle(value);
      },
    );
  }

  Widget _buildContentInput(QRProvider qrProvider) {
    switch (qrProvider.currentType) {
      case QRCodeType.url:
        return TextFormField(
          controller: _contentController,
          decoration: const InputDecoration(
            labelText: 'URL *',
            hintText: 'https://example.com',
            prefixIcon: Icon(Icons.link),
          ),
          keyboardType: TextInputType.url,
          validator: (value) {
            if (value == null || value.trim().isEmpty) {
              return 'URL is required';
            }
            if (!QRGeneratorService.validateQRData(value, QRCodeType.url)) {
              return 'Please enter a valid URL';
            }
            return null;
          },
          onChanged: (value) {
            qrProvider.updateContent(value);
          },
        );

      case QRCodeType.email:
        return TextFormField(
          controller: _contentController,
          decoration: const InputDecoration(
            labelText: 'Email Address *',
            hintText: '<EMAIL>',
            prefixIcon: Icon(Icons.email),
          ),
          keyboardType: TextInputType.emailAddress,
          validator: (value) {
            if (value == null || value.trim().isEmpty) {
              return 'Email is required';
            }
            if (!QRGeneratorService.validateQRData(value, QRCodeType.email)) {
              return 'Please enter a valid email address';
            }
            return null;
          },
          onChanged: (value) {
            qrProvider.updateContent(value);
          },
        );

      case QRCodeType.phone:
        return TextFormField(
          controller: _contentController,
          decoration: const InputDecoration(
            labelText: 'Phone Number *',
            hintText: '+**********',
            prefixIcon: Icon(Icons.phone),
          ),
          keyboardType: TextInputType.phone,
          validator: (value) {
            if (value == null || value.trim().isEmpty) {
              return 'Phone number is required';
            }
            if (!QRGeneratorService.validateQRData(value, QRCodeType.phone)) {
              return 'Please enter a valid phone number';
            }
            return null;
          },
          onChanged: (value) {
            qrProvider.updateContent(value);
          },
        );

      case QRCodeType.sms:
        return TextFormField(
          controller: _contentController,
          decoration: const InputDecoration(
            labelText: 'Phone Number *',
            hintText: '+**********',
            prefixIcon: Icon(Icons.sms),
          ),
          keyboardType: TextInputType.phone,
          validator: (value) {
            if (value == null || value.trim().isEmpty) {
              return 'Phone number is required';
            }
            return null;
          },
          onChanged: (value) {
            qrProvider.updateContent(value);
          },
        );

      default:
        return TextFormField(
          controller: _contentController,
          decoration: const InputDecoration(
            labelText: 'Content *',
            hintText: 'Enter your content here',
            prefixIcon: Icon(Icons.text_fields),
          ),
          maxLines: 3,
          validator: (value) {
            if (value == null || value.trim().isEmpty) {
              return 'Content is required';
            }
            return null;
          },
          onChanged: (value) {
            qrProvider.updateContent(value);
          },
        );
    }
  }

  List<Widget> _buildAdditionalFields(QRProvider qrProvider) {
    switch (qrProvider.currentType) {
      case QRCodeType.email:
        return [
          TextFormField(
            controller: _emailSubjectController,
            decoration: const InputDecoration(
              labelText: 'Subject (Optional)',
              hintText: 'Email subject',
              prefixIcon: Icon(Icons.subject),
            ),
          ),
          const SizedBox(height: 16),
          TextFormField(
            controller: _emailBodyController,
            decoration: const InputDecoration(
              labelText: 'Body (Optional)',
              hintText: 'Email body',
              prefixIcon: Icon(Icons.message),
            ),
            maxLines: 3,
          ),
          const SizedBox(height: 16),
        ];

      case QRCodeType.sms:
        return [
          TextFormField(
            controller: _smsMessageController,
            decoration: const InputDecoration(
              labelText: 'Message (Optional)',
              hintText: 'SMS message',
              prefixIcon: Icon(Icons.message),
            ),
            maxLines: 3,
          ),
          const SizedBox(height: 16),
        ];

      default:
        return [];
    }
  }

  Widget _buildDescriptionInput(QRProvider qrProvider) {
    return TextFormField(
      controller: _descriptionController,
      decoration: const InputDecoration(
        labelText: 'Description (Optional)',
        hintText: 'Add a description for this QR code',
        prefixIcon: Icon(Icons.description),
      ),
      maxLines: 2,
      onChanged: (value) {
        qrProvider.updateDescription(value);
      },
    );
  }

  Widget _buildQuickTemplates(QRProvider qrProvider) {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Quick Templates',
              style: Theme.of(
                context,
              ).textTheme.titleSmall?.copyWith(fontWeight: FontWeight.bold),
            ),
            const SizedBox(height: 12),
            Wrap(
              spacing: 8,
              runSpacing: 8,
              children:
                  _getQuickTemplates(qrProvider.currentType).map((template) {
                    return ActionChip(
                      label: Text(template['name'] ?? ''),
                      onPressed: () {
                        _applyTemplate(template, qrProvider);
                      },
                    );
                  }).toList(),
            ),
          ],
        ),
      ),
    );
  }

  List<Map<String, String>> _getQuickTemplates(QRCodeType type) {
    switch (type) {
      case QRCodeType.url:
        return [
          {'name': 'Website', 'content': 'https://'},
          {'name': 'YouTube', 'content': 'https://youtube.com/'},
          {'name': 'LinkedIn', 'content': 'https://linkedin.com/in/'},
        ];
      case QRCodeType.email:
        return [
          {'name': 'Contact Me', 'content': '<EMAIL>'},
          {'name': 'Support', 'content': '<EMAIL>'},
        ];
      case QRCodeType.phone:
        return [
          {'name': 'Business', 'content': '+1-'},
          {'name': 'Emergency', 'content': '911'},
        ];
      default:
        return [];
    }
  }

  void _applyTemplate(Map<String, String> template, QRProvider qrProvider) {
    _contentController.text = template['content'] ?? '';
    qrProvider.updateContent(template['content'] ?? '');
  }

  void _clearControllers() {
    _contentController.clear();
    _emailSubjectController.clear();
    _emailBodyController.clear();
    _smsMessageController.clear();
  }

  String _getTypeDisplayName(QRCodeType type) {
    switch (type) {
      case QRCodeType.text:
        return 'Text';
      case QRCodeType.url:
        return 'URL';
      case QRCodeType.email:
        return 'Email';
      case QRCodeType.phone:
        return 'Phone';
      case QRCodeType.sms:
        return 'SMS';
      case QRCodeType.wifi:
        return 'WiFi';
      case QRCodeType.contact:
        return 'Contact';
      case QRCodeType.location:
        return 'Location';
    }
  }

  IconData _getTypeIcon(QRCodeType type) {
    switch (type) {
      case QRCodeType.text:
        return Icons.text_fields;
      case QRCodeType.url:
        return Icons.link;
      case QRCodeType.email:
        return Icons.email;
      case QRCodeType.phone:
        return Icons.phone;
      case QRCodeType.sms:
        return Icons.sms;
      case QRCodeType.wifi:
        return Icons.wifi;
      case QRCodeType.contact:
        return Icons.contact_page;
      case QRCodeType.location:
        return Icons.location_on;
    }
  }
}
